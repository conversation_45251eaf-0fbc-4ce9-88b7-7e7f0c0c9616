/* Canvas-Inspired Modern Professional UI */

/* CSS Variables for Canvas-like Design */
:root {
    /* Canvas Color Palette */
    --canvas-primary: #2d3748;
    --canvas-secondary: #4a5568;
    --canvas-accent: #3182ce;
    --canvas-accent-light: #63b3ed;
    --canvas-success: #38a169;
    --canvas-warning: #d69e2e;
    --canvas-error: #e53e3e;

    /* Background Colors */
    --canvas-bg-primary: #f7fafc;
    --canvas-bg-secondary: #edf2f7;
    --canvas-bg-tertiary: #e2e8f0;
    --canvas-white: #ffffff;
    --canvas-gray-50: #f9fafb;
    --canvas-gray-100: #f3f4f6;
    --canvas-gray-200: #e5e7eb;
    --canvas-gray-300: #d1d5db;
    --canvas-gray-400: #9ca3af;
    --canvas-gray-500: #6b7280;
    --canvas-gray-600: #4b5563;
    --canvas-gray-700: #374151;
    --canvas-gray-800: #1f2937;
    --canvas-gray-900: #111827;

    /* Text Colors */
    --canvas-text-primary: #1a202c;
    --canvas-text-secondary: #4a5568;
    --canvas-text-muted: #718096;
    --canvas-text-light: #a0aec0;
    --canvas-text-white: #ffffff;

    /* Border Colors */
    --canvas-border-light: #e2e8f0;
    --canvas-border-medium: #cbd5e0;
    --canvas-border-dark: #a0aec0;

    /* Shadows */
    --canvas-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --canvas-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --canvas-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --canvas-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Spacing System */
    --canvas-space-1: 0.25rem;
    --canvas-space-2: 0.5rem;
    --canvas-space-3: 0.75rem;
    --canvas-space-4: 1rem;
    --canvas-space-5: 1.25rem;
    --canvas-space-6: 1.5rem;
    --canvas-space-8: 2rem;
    --canvas-space-10: 2.5rem;
    --canvas-space-12: 3rem;
    --canvas-space-16: 4rem;
    --canvas-space-20: 5rem;

    /* Typography */
    --canvas-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --canvas-font-size-xs: 0.75rem;
    --canvas-font-size-sm: 0.875rem;
    --canvas-font-size-base: 1rem;
    --canvas-font-size-lg: 1.125rem;
    --canvas-font-size-xl: 1.25rem;
    --canvas-font-size-2xl: 1.5rem;
    --canvas-font-size-3xl: 1.875rem;
    --canvas-font-size-4xl: 2.25rem;

    /* Font Weights */
    --canvas-font-weight-normal: 400;
    --canvas-font-weight-medium: 500;
    --canvas-font-weight-semibold: 600;
    --canvas-font-weight-bold: 700;

    /* Border Radius */
    --canvas-radius-sm: 0.25rem;
    --canvas-radius-md: 0.375rem;
    --canvas-radius-lg: 0.5rem;
    --canvas-radius-xl: 0.75rem;
    --canvas-radius-2xl: 1rem;
    --canvas-radius-full: 9999px;

    /* Transitions */
    --canvas-transition-fast: 150ms ease-in-out;
    --canvas-transition-normal: 200ms ease-in-out;
    --canvas-transition-slow: 300ms ease-in-out;

    /* Grid Pattern */
    --canvas-grid-size: 20px;
    --canvas-grid-color: rgba(45, 55, 72, 0.05);
}

/* Reset and Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--canvas-font-family);
    font-size: var(--canvas-font-size-base);
    font-weight: var(--canvas-font-weight-normal);
    line-height: 1.6;
    color: var(--canvas-text-primary);
    background-color: var(--canvas-bg-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* Canvas Grid Background */
    background-image:
        linear-gradient(var(--canvas-grid-color) 1px, transparent 1px),
        linear-gradient(90deg, var(--canvas-grid-color) 1px, transparent 1px);
    background-size: var(--canvas-grid-size) var(--canvas-grid-size);
    background-position: 0 0, 0 0;
    min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--canvas-font-weight-semibold);
    line-height: 1.25;
    margin-bottom: var(--canvas-space-4);
    color: var(--canvas-text-primary);
}

h1 { font-size: var(--canvas-font-size-3xl); }
h2 { font-size: var(--canvas-font-size-2xl); }
h3 { font-size: var(--canvas-font-size-xl); }
h4 { font-size: var(--canvas-font-size-lg); }
h5 { font-size: var(--canvas-font-size-base); }
h6 { font-size: var(--canvas-font-size-sm); }

p {
    margin-bottom: var(--canvas-space-4);
    color: var(--canvas-text-secondary);
}

a {
    color: var(--canvas-accent);
    text-decoration: none;
    transition: color var(--canvas-transition-fast);
}

a:hover {
    color: var(--canvas-accent-light);
}

/* Main Layout */
.main-content {
    display: grid;
    grid-template-columns: 240px 1fr 320px;
    min-height: 100vh;
    gap: 0;
    background: transparent;
}

/* Sidebar */
.side-bar {
    background: var(--canvas-white);
    border-right: 1px solid var(--canvas-border-light);
    padding: var(--canvas-space-6) 0;
    box-shadow: var(--canvas-shadow-sm);
    position: relative;
    z-index: 10;
}

.tool-category {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 64px;
    margin-bottom: var(--canvas-space-2);
    color: var(--canvas-text-muted);
    font-size: 24px;
    cursor: pointer;
    transition: all var(--canvas-transition-normal);
    position: relative;
    border: none;
    background: transparent;
}

.tool-category:hover {
    background: var(--canvas-bg-secondary);
    color: var(--canvas-accent);
    transform: translateX(4px);
}

.tool-category[aria-selected="true"] {
    background: var(--canvas-accent);
    color: var(--canvas-white);
    box-shadow: var(--canvas-shadow-md);
}

.tool-category[aria-selected="true"]::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--canvas-accent-light);
}

/* Tooltip for sidebar icons */
.tool-category::after {
    content: attr(data-label);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: var(--canvas-gray-800);
    color: var(--canvas-white);
    padding: var(--canvas-space-2) var(--canvas-space-3);
    border-radius: var(--canvas-radius-md);
    font-size: var(--canvas-font-size-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--canvas-transition-fast);
    margin-left: var(--canvas-space-2);
    z-index: 1000;
}

.tool-category:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Middle Section - Tools and AutoSpace */
.middle-section {
    background: transparent;
    padding: var(--canvas-space-6);
    overflow-y: auto;
}

.section {
    margin-bottom: var(--canvas-space-8);
}

/* Tools Cards Section */
.tools-cards-section {
    background: var(--canvas-white);
    border-radius: var(--canvas-radius-xl);
    padding: var(--canvas-space-6);
    box-shadow: var(--canvas-shadow-md);
    border: 1px solid var(--canvas-border-light);
    margin-bottom: var(--canvas-space-6);
}

.tools-cards-section h1 {
    color: var(--canvas-text-primary);
    font-size: var(--canvas-font-size-2xl);
    font-weight: var(--canvas-font-weight-bold);
    margin-bottom: var(--canvas-space-6);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 2px solid var(--canvas-accent);
    padding-bottom: var(--canvas-space-3);
}

.tool-card {
    background: var(--canvas-white);
    border: 1px solid var(--canvas-border-light);
    border-radius: var(--canvas-radius-lg);
    padding: var(--canvas-space-5);
    margin-bottom: var(--canvas-space-4);
    cursor: pointer;
    transition: all var(--canvas-transition-normal);
    position: relative;
    overflow: hidden;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--canvas-accent), var(--canvas-accent-light));
    transform: scaleX(0);
    transition: transform var(--canvas-transition-normal);
}

.tool-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--canvas-shadow-lg);
    border-color: var(--canvas-accent);
}

.tool-card:hover::before {
    transform: scaleX(1);
}

.tool-card.selected {
    border-color: var(--canvas-accent);
    box-shadow: var(--canvas-shadow-lg);
    background: linear-gradient(135deg, var(--canvas-white) 0%, var(--canvas-bg-secondary) 100%);
}

.tool-card.selected::before {
    transform: scaleX(1);
}

.tool-card h3 {
    color: var(--canvas-text-primary);
    font-size: var(--canvas-font-size-lg);
    font-weight: var(--canvas-font-weight-semibold);
    margin-bottom: var(--canvas-space-2);
}

.tool-card-preview {
    color: var(--canvas-text-muted);
    font-size: var(--canvas-font-size-sm);
    line-height: 1.5;
}

/* AutoSpace Section */
.autospace-section {
    background: var(--canvas-white);
    border-radius: var(--canvas-radius-xl);
    padding: var(--canvas-space-6);
    box-shadow: var(--canvas-shadow-md);
    border: 1px solid var(--canvas-border-light);
    margin-bottom: var(--canvas-space-6);
}

.autospace-section h2 {
    color: var(--canvas-text-primary);
    font-size: var(--canvas-font-size-2xl);
    font-weight: var(--canvas-font-weight-bold);
    margin-bottom: var(--canvas-space-6);
    text-align: center;
    position: relative;
}

.autospace-section h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--canvas-accent), var(--canvas-accent-light));
    border-radius: var(--canvas-radius-full);
}

.default-view {
    text-align: center;
    padding: var(--canvas-space-12) var(--canvas-space-6);
    color: var(--canvas-text-muted);
    font-size: var(--canvas-font-size-lg);
    background: var(--canvas-bg-secondary);
    border-radius: var(--canvas-radius-lg);
    border: 2px dashed var(--canvas-border-medium);
}

.selection-view {
    display: none;
    flex-direction: column;
    gap: var(--canvas-space-6);
}

.tool-name {
    font-size: var(--canvas-font-size-xl);
    font-weight: var(--canvas-font-weight-bold);
    color: var(--canvas-text-primary);
    margin-bottom: var(--canvas-space-3);
}

.tool-description {
    color: var(--canvas-text-secondary);
    font-size: var(--canvas-font-size-base);
    line-height: 1.6;
    margin-bottom: var(--canvas-space-6);
    padding: var(--canvas-space-4);
    background: var(--canvas-bg-secondary);
    border-radius: var(--canvas-radius-lg);
    border-left: 4px solid var(--canvas-accent);
}

.tool-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--canvas-space-4);
    align-items: center;
    padding: var(--canvas-space-4);
    background: var(--canvas-bg-secondary);
    border-radius: var(--canvas-radius-lg);
}

/* Button Styles */
.run-button, .download-sample-button {
    background: linear-gradient(135deg, var(--canvas-accent), var(--canvas-accent-light));
    color: var(--canvas-white);
    border: none;
    padding: var(--canvas-space-3) var(--canvas-space-6);
    border-radius: var(--canvas-radius-lg);
    font-size: var(--canvas-font-size-sm);
    font-weight: var(--canvas-font-weight-medium);
    cursor: pointer;
    transition: all var(--canvas-transition-normal);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: var(--canvas-shadow-sm);
}

.run-button:hover, .download-sample-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--canvas-shadow-lg);
}

.run-button:active, .download-sample-button:active {
    transform: translateY(0);
}

/* File Upload Styling */
.file-label {
    background: var(--canvas-white);
    color: var(--canvas-text-primary);
    border: 2px solid var(--canvas-border-medium);
    padding: var(--canvas-space-3) var(--canvas-space-6);
    border-radius: var(--canvas-radius-lg);
    font-size: var(--canvas-font-size-sm);
    font-weight: var(--canvas-font-weight-medium);
    cursor: pointer;
    transition: all var(--canvas-transition-normal);
    display: inline-flex;
    align-items: center;
    gap: var(--canvas-space-2);
}

.file-label:hover {
    border-color: var(--canvas-accent);
    background: var(--canvas-bg-secondary);
    transform: translateY(-1px);
}

#fileInput {
    display: none;
}

/* Checkbox Styling */
input[type="checkbox"] {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--canvas-border-medium);
    border-radius: var(--canvas-radius-sm);
    background: var(--canvas-white);
    cursor: pointer;
    transition: all var(--canvas-transition-fast);
    position: relative;
}

input[type="checkbox"]:checked {
    background: var(--canvas-accent);
    border-color: var(--canvas-accent);
}

input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--canvas-white);
    font-size: 12px;
    font-weight: bold;
}

label[for="mode"] {
    color: var(--canvas-text-secondary);
    font-size: var(--canvas-font-size-sm);
    font-weight: var(--canvas-font-weight-medium);
    cursor: pointer;
    margin-left: var(--canvas-space-2);
}

/* Sessions Section */
.sessions-section {
    background: var(--canvas-white);
    border-radius: var(--canvas-radius-xl);
    padding: var(--canvas-space-6);
    box-shadow: var(--canvas-shadow-md);
    border: 1px solid var(--canvas-border-light);
    display: flex;
    flex-direction: column;
    height: 400px;
}

.sessions-container {
    flex: 1;
    overflow-y: auto;
    padding-right: var(--canvas-space-2);
    margin-bottom: var(--canvas-space-4);
}

.sessions-container h2 {
    color: var(--canvas-text-primary);
    font-size: var(--canvas-font-size-lg);
    font-weight: var(--canvas-font-weight-semibold);
    margin-bottom: var(--canvas-space-4);
    padding-bottom: var(--canvas-space-2);
    border-bottom: 1px solid var(--canvas-border-light);
}

.session-card {
    background: var(--canvas-white);
    border: 1px solid var(--canvas-border-light);
    border-radius: var(--canvas-radius-lg);
    padding: var(--canvas-space-4);
    margin-bottom: var(--canvas-space-3);
    transition: all var(--canvas-transition-normal);
    position: relative;
    overflow: hidden;
}

.session-card::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--canvas-accent);
    transition: all var(--canvas-transition-normal);
}

.session-card:hover {
    transform: translateX(4px);
    box-shadow: var(--canvas-shadow-md);
    border-color: var(--canvas-accent);
}

.session-card .title {
    font-size: var(--canvas-font-size-base);
    font-weight: var(--canvas-font-weight-semibold);
    color: var(--canvas-text-primary);
    margin-bottom: var(--canvas-space-2);
}

.session-card .time {
    font-size: var(--canvas-font-size-xs);
    color: var(--canvas-text-muted);
    background: var(--canvas-bg-secondary);
    padding: var(--canvas-space-1) var(--canvas-space-2);
    border-radius: var(--canvas-radius-full);
    display: inline-block;
    margin-bottom: var(--canvas-space-3);
}

.session-card .progress-counter {
    font-size: var(--canvas-font-size-sm);
    font-weight: var(--canvas-font-weight-medium);
    color: var(--canvas-accent);
    background: var(--canvas-bg-secondary);
    padding: var(--canvas-space-1) var(--canvas-space-3);
    border-radius: var(--canvas-radius-full);
    display: inline-block;
    margin-bottom: var(--canvas-space-3);
}

.session-card button {
    background: var(--canvas-white);
    border: 1px solid var(--canvas-border-medium);
    color: var(--canvas-text-secondary);
    padding: var(--canvas-space-2) var(--canvas-space-3);
    border-radius: var(--canvas-radius-md);
    font-size: var(--canvas-font-size-xs);
    font-weight: var(--canvas-font-weight-medium);
    cursor: pointer;
    transition: all var(--canvas-transition-fast);
    margin-right: var(--canvas-space-2);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.session-card .refresh-button:hover {
    background: var(--canvas-accent);
    color: var(--canvas-white);
    border-color: var(--canvas-accent);
}

.session-card .kill-button:hover {
    background: var(--canvas-error);
    color: var(--canvas-white);
    border-color: var(--canvas-error);
}

.session-card .download-button:hover {
    background: var(--canvas-success);
    color: var(--canvas-white);
    border-color: var(--canvas-success);
}

/* Custom Scrollbar */
.sessions-container::-webkit-scrollbar {
    width: 6px;
}

.sessions-container::-webkit-scrollbar-track {
    background: var(--canvas-bg-secondary);
    border-radius: var(--canvas-radius-full);
}

.sessions-container::-webkit-scrollbar-thumb {
    background: var(--canvas-border-medium);
    border-radius: var(--canvas-radius-full);
}

.sessions-container::-webkit-scrollbar-thumb:hover {
    background: var(--canvas-accent);
}

/* Right Section - System Performance & Users */
.right-section {
    background: transparent;
    padding: var(--canvas-space-6);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--canvas-space-6);
}

/* System Performance Section */
.system-performance-section {
    background: var(--canvas-white);
    border-radius: var(--canvas-radius-xl);
    padding: var(--canvas-space-6);
    box-shadow: var(--canvas-shadow-md);
    border: 1px solid var(--canvas-border-light);
}

.box-title {
    display: flex;
    align-items: center;
    gap: var(--canvas-space-2);
    font-size: var(--canvas-font-size-lg);
    font-weight: var(--canvas-font-weight-semibold);
    color: var(--canvas-text-primary);
    margin-bottom: var(--canvas-space-4);
    padding-bottom: var(--canvas-space-2);
    border-bottom: 1px solid var(--canvas-border-light);
}

.box-title i {
    color: var(--canvas-accent);
}

.performance-box {
    padding: 0;
    background: transparent;
    border: none;
    margin: 0;
}

.grid-2x2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--canvas-space-4);
}

.metric-card {
    background: var(--canvas-bg-secondary);
    border-radius: var(--canvas-radius-lg);
    padding: var(--canvas-space-4);
    display: flex;
    align-items: center;
    gap: var(--canvas-space-3);
    transition: all var(--canvas-transition-normal);
    border: 1px solid var(--canvas-border-light);
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--canvas-shadow-md);
    background: var(--canvas-white);
}

.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--canvas-radius-lg);
    background: linear-gradient(135deg, var(--canvas-accent), var(--canvas-accent-light));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--canvas-white);
    font-size: 18px;
}

.metric-content {
    flex: 1;
}

.metric-label {
    display: block;
    font-size: var(--canvas-font-size-xs);
    color: var(--canvas-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--canvas-space-1);
}

.metric-value {
    font-size: var(--canvas-font-size-base);
    font-weight: var(--canvas-font-weight-semibold);
    color: var(--canvas-text-primary);
}

/* Active Users Section */
.active-users-section {
    background: var(--canvas-white);
    border-radius: var(--canvas-radius-xl);
    padding: var(--canvas-space-6);
    box-shadow: var(--canvas-shadow-md);
    border: 1px solid var(--canvas-border-light);
    flex: 1;
}

.user-count {
    background: var(--canvas-accent);
    color: var(--canvas-white);
    padding: var(--canvas-space-1) var(--canvas-space-2);
    border-radius: var(--canvas-radius-full);
    font-size: var(--canvas-font-size-xs);
    font-weight: var(--canvas-font-weight-semibold);
    margin-left: var(--canvas-space-2);
}

.users-box {
    padding: 0;
    background: transparent;
    border: none;
    margin: 0;
    height: 300px;
    overflow-y: auto;
}

.users-container {
    height: 100%;
    overflow-y: auto;
    padding-right: var(--canvas-space-2);
}

.user-card {
    background: var(--canvas-bg-secondary);
    border-radius: var(--canvas-radius-lg);
    padding: var(--canvas-space-4);
    margin-bottom: var(--canvas-space-3);
    transition: all var(--canvas-transition-normal);
    border: 1px solid var(--canvas-border-light);
    position: relative;
    overflow: hidden;
}

.user-card::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--canvas-accent);
    transition: all var(--canvas-transition-normal);
}

.user-card.current-user::before {
    background: var(--canvas-success);
}

.user-card:hover {
    transform: translateX(4px);
    box-shadow: var(--canvas-shadow-md);
    background: var(--canvas-white);
}

.user-header {
    display: flex;
    align-items: center;
    gap: var(--canvas-space-2);
    margin-bottom: var(--canvas-space-2);
}

.user-header i {
    color: var(--canvas-accent);
    font-size: 18px;
}

.username {
    flex: 1;
    font-size: var(--canvas-font-size-sm);
    font-weight: var(--canvas-font-weight-semibold);
    color: var(--canvas-text-primary);
}

.session-badge {
    background: var(--canvas-accent);
    color: var(--canvas-white);
    padding: var(--canvas-space-1) var(--canvas-space-2);
    border-radius: var(--canvas-radius-full);
    font-size: var(--canvas-font-size-xs);
    font-weight: var(--canvas-font-weight-medium);
}

.user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--canvas-font-size-xs);
    color: var(--canvas-text-muted);
}

.last-activity {
    font-style: italic;
}

.activity-type {
    background: var(--canvas-white);
    padding: var(--canvas-space-1) var(--canvas-space-2);
    border-radius: var(--canvas-radius-sm);
    font-weight: var(--canvas-font-weight-medium);
    text-transform: capitalize;
}

.no-users {
    text-align: center;
    padding: var(--canvas-space-12) var(--canvas-space-4);
    color: var(--canvas-text-muted);
}

.no-users i {
    font-size: 2rem;
    margin-bottom: var(--canvas-space-4);
    opacity: 0.5;
}

.no-users p {
    margin: 0;
    font-size: var(--canvas-font-size-sm);
}

/* Custom Scrollbar for Users */
.users-container::-webkit-scrollbar {
    width: 6px;
}

.users-container::-webkit-scrollbar-track {
    background: var(--canvas-bg-secondary);
    border-radius: var(--canvas-radius-full);
}

.users-container::-webkit-scrollbar-thumb {
    background: var(--canvas-border-medium);
    border-radius: var(--canvas-radius-full);
}

.users-container::-webkit-scrollbar-thumb:hover {
    background: var(--canvas-accent);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 200px 1fr 280px;
    }

    .grid-2x2 {
        grid-template-columns: 1fr;
        gap: var(--canvas-space-3);
    }
}

@media (max-width: 992px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }

    .side-bar {
        padding: var(--canvas-space-4);
        display: flex;
        flex-direction: row;
        justify-content: center;
        gap: var(--canvas-space-4);
        border-right: none;
        border-bottom: 1px solid var(--canvas-border-light);
    }

    .tool-category {
        width: auto;
        height: 48px;
        padding: 0 var(--canvas-space-4);
        border-radius: var(--canvas-radius-lg);
    }

    .tool-category:hover {
        transform: translateY(-2px);
    }

    .tool-category::after {
        display: none;
    }

    .right-section {
        order: 3;
        flex-direction: row;
        gap: var(--canvas-space-4);
    }

    .system-performance-section,
    .active-users-section {
        flex: 1;
    }
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
    }

    .side-bar {
        flex-wrap: wrap;
        gap: var(--canvas-space-2);
    }

    .tool-category {
        height: 40px;
        padding: 0 var(--canvas-space-3);
        font-size: 20px;
    }

    .middle-section,
    .right-section {
        padding: var(--canvas-space-4);
    }

    .right-section {
        flex-direction: column;
    }

    .tool-buttons {
        flex-direction: column;
        align-items: stretch;
    }

    .tool-buttons > * {
        width: 100%;
        text-align: center;
    }

    .grid-2x2 {
        grid-template-columns: 1fr;
    }
}

/* Focus States for Accessibility */
.tool-category:focus,
button:focus,
input:focus,
label:focus {
    outline: 2px solid var(--canvas-accent);
    outline-offset: 2px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--canvas-border-light);
    border-top-color: var(--canvas-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Print Styles */
@media print {
    .side-bar,
    .right-section {
        display: none;
    }

    .main-content {
        grid-template-columns: 1fr;
    }

    body {
        background: white;
        background-image: none;
    }
}